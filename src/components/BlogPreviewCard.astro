---
interface Props {
  imageUrl: string;
  date: string;
  title: string;
  link: string;
}
const { imageUrl, date, title, link } = Astro.props;
---
<a href={link} class="group block">
  <div class="overflow-hidden rounded-lg mb-4">
    <img src={imageUrl} alt={title} class="w-full h-60 object-cover group-hover:scale-110 transition-transform duration-300"/>
  </div>
  <p class="text-sm text-brand-text-muted mb-2">{date}</p>
  <h3 class="text-xl mb-3 group-hover:text-brand-accent transition-colors">{title}</h3>
  <span class="font-bold text-brand-accent">Read Article</span>
</a>
