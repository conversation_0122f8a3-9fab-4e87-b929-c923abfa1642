---
interface Props {
  sign: string;
  dateRange: string;
  forecast: string;
  icon: string; // The zodiac symbol
}
const { sign, dateRange, forecast, icon } = Astro.props;
---
<div class="bg-brand-purple/50 border border-brand-light-purple/30 rounded-xl p-8 backdrop-blur-sm transition-all duration-300 hover:border-brand-accent hover:-translate-y-2">
  <div class="flex items-center mb-4">
    <span class="text-5xl text-brand-accent mr-4">{icon}</span>
    <div>
      <h3 class="text-2xl">{sign}</h3>
      <p class="text-brand-text-muted">{dateRange}</p>
    </div>
  </div>
  <p class="text-brand-text-muted">{forecast}</p>
</div>
