---
// Theme switcher component
---

<button id="theme-toggle" class="theme-toggle" title="Toggle theme">
  <svg id="sun-icon" class="theme-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
  </svg>
  <svg id="moon-icon" class="theme-icon hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
  </svg>
</button>

<style>
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: transparent;
  border: 1px solid var(--color-brand-light-purple);
  border-radius: 0.5rem;
  color: var(--color-brand-text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  border-color: var(--color-brand-accent);
  color: var(--color-brand-accent);
}

.theme-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.hidden {
  display: none;
}
</style>

<script>
import { getCurrentTheme, setCurrentTheme } from '../utils/i18n';

document.addEventListener('DOMContentLoaded', () => {
  const themeToggle = document.getElementById('theme-toggle');
  const sunIcon = document.getElementById('sun-icon');
  const moonIcon = document.getElementById('moon-icon');

  // Set initial theme
  const currentTheme = getCurrentTheme();
  updateThemeDisplay(currentTheme);
  
  // Apply theme to document
  if (currentTheme === 'light') {
    document.documentElement.classList.add('light-theme');
  }

  function updateThemeDisplay(theme: 'light' | 'dark') {
    if (theme === 'light') {
      sunIcon?.classList.add('hidden');
      moonIcon?.classList.remove('hidden');
    } else {
      sunIcon?.classList.remove('hidden');
      moonIcon?.classList.add('hidden');
    }
  }

  themeToggle?.addEventListener('click', () => {
    const currentTheme = getCurrentTheme();
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    setCurrentTheme(newTheme);
    updateThemeDisplay(newTheme);
  });
});
</script>
