---
interface Props {
    title: string;
    subtitle: string;
    price: string;
    features: string[];
    popular?: boolean;
}
const { title, subtitle, price, features, popular = false } = Astro.props;
---
<div class:list={[
  'bg-brand-purple/50 border border-brand-light-purple/30 rounded-xl p-8 relative overflow-hidden h-full flex flex-col',
  { 'border-brand-accent': popular }
]}>
    {popular && (
        <div class="absolute top-0 right-0 bg-brand-accent text-brand-dark font-bold text-sm px-4 py-1 rounded-bl-lg">Popular</div>
    )}
    <h3 class="text-2xl mb-1">{title}</h3>
    <p class="text-brand-text-muted mb-6">{subtitle}</p>

    <p class="text-5xl font-bold mb-6">${price}<span class="text-lg font-sans text-brand-text-muted">/month</span></p>

    <ul class="space-y-4 mb-8">
        {features.map(feature => (
            <li class="flex items-start">
                <span class="text-brand-accent mr-3 mt-1">✓</span>
                <span class="text-brand-text-muted">{feature}</span>
            </li>
        ))}
    </ul>

    <a href="#" class:list={[
      "w-full text-center block font-bold py-3 px-6 rounded-lg transition-colors mt-auto",
      {
        'bg-brand-accent text-brand-dark hover:bg-opacity-90': popular,
        'bg-brand-light-purple text-white hover:bg-opacity-80': !popular,
      }
    ]}>
      Subscribe Now
    </a>
</div>
