---
interface Props {
  title: string;
  description: string;
}
const { title, description } = Astro.props;
---
<div class="bg-brand-purple/50 border border-brand-light-purple/30 rounded-xl p-8 backdrop-blur-sm transition-all duration-300 hover:border-brand-accent hover:scale-105">
  <div class="w-12 h-12 bg-brand-light-purple/50 rounded-full mb-6 flex items-center justify-center">
    <span class="text-2xl">✨</span>
  </div>
  <h3 class="text-2xl mb-4">{title}</h3>
  <p class="text-brand-text-muted">{description}</p>
</div>
