@import "tailwindcss";

@layer base {
  /* Here we define our custom colors as CSS Variables */
  :root {
    --color-brand-dark: #0d0b1c;
    --color-brand-purple: #1c163b;
    --color-brand-light-purple: #3c2c8b;
    --color-brand-accent: #d4ff00;
    --color-brand-text: #ffffff;
    --color-brand-text-muted: #c0c0c0;
  }

  /* Light theme variables */
  .light-theme {
    --color-brand-dark: #ffffff;
    --color-brand-purple: #f8f9fa;
    --color-brand-light-purple: #e9ecef;
    --color-brand-accent: #6f42c1;
    --color-brand-text: #212529;
    --color-brand-text-muted: #6c757d;
  }

  html {
    @apply scroll-smooth;
  }
  
  /* We use our CSS variables to set the defaults */
  body {
    @apply bg-[var(--color-brand-dark)] text-[var(--color-brand-text)] font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }
}
