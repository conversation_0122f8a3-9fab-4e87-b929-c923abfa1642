---
import Layout from '../layouts/Layout.astro';
import ServiceCard from '../components/ServiceCard.astro';
import HoroscopeCard from '../components/HoroscopeCard.astro';
import BlogPreviewCard from '../components/BlogPreviewCard.astro';
import MembershipPlan from '../components/MembershipPlan.astro';
import { getCurrentLanguage } from '../utils/i18n';
import { getTranslations } from '../data/translations';

const currentLang = getCurrentLanguage();
const t = getTranslations(currentLang);

const services = [
  { title: t.services.expertAstrologers, description: t.services.expertAstrologersDesc },
  { title: t.services.compatibilityReadings, description: t.services.compatibilityReadingsDesc },
  { title: t.services.progressionReadings, description: t.services.progressionReadingsDesc }
];
const horoscopes = [
    { sign: 'Aries', dateRange: 'March 21 - April 19', forecast: 'A surge of creative energy propels you forward. Trust your instincts and take that leap of faith.', icon: '♈' },
    { sign: 'Taurus', dateRange: 'April 20 - May 20', forecast: 'Focus on grounding yourself. Financial matters may come to the forefront; a practical approach will serve you well.', icon: '♉' },
    { sign: 'Gemini', dateRange: 'May 21 - June 20', forecast: 'Communication is your superpower this week. Express your ideas clearly, and you\'ll find others are receptive.', icon: '♊' },
    { sign: 'Cancer', dateRange: 'June 21 - July 22', forecast: 'Home and family take center stage. It\'s an excellent time for nurturing your personal space and relationships.', icon: '♋' },
    { sign: 'Leo', dateRange: 'July 23 - August 22', forecast: 'Your natural charisma shines brightly. Step into the spotlight and share your talents with the world.', icon: '♌' },
    { sign: 'Virgo', dateRange: 'August 23 - September 22', forecast: 'Organization is key. Tidy up your physical and mental spaces to make way for new opportunities.', icon: '♍' },
];
const blogPosts = [
    { imageUrl: "https://placehold.co/600x400/0d0b1c/d4ff00?text=Cosmic+Image+1", date: "June 2024", title: "The Astrology of Success", link: "#" },
    { imageUrl: "https://placehold.co/600x400/0d0b1c/d4ff00?text=Cosmic+Image+2", date: "June 2024", title: "A Celestial Guide to Managing Stress", link: "#" },
    { imageUrl: "https://placehold.co/600x400/0d0b1c/d4ff00?text=Cosmic+Image+3", date: "May 2024", title: "Understanding Your Moon Sign", link: "#" },
];
---
<Layout title="Celestial - Astrology & Horoscope">
  <!-- Hero Section -->
  <section class="relative h-screen flex items-center justify-center text-center overflow-hidden bg-hero-gradient">
    <div class="absolute inset-0 z-0" id="stars"></div>
    <div class="relative z-10 px-6">
      <h1 class="text-5xl md:text-7xl lg:text-8xl font-bold leading-tight mb-4" data-i18n="Unveiling the Universe Within You">Раскрываем Вселенную<br>Внутри Вас</h1>
      <p class="text-lg md:text-xl text-[var(--color-brand-text-muted)] mb-8" data-i18n="Celestial helps you exploring your cosmic path">Celestial поможет вам исследовать ваш космический путь</p>
      <div class="flex flex-col sm:flex-row justify-center gap-4">
        <a href="/services" class="bg-[var(--color-brand-accent)] text-[var(--color-brand-dark)] font-bold py-3 px-8 rounded-lg hover:bg-opacity-90 transition-colors" data-i18n="Book a Session">Записаться на сеанс</a>
        <a href="/horoscope" class="bg-transparent border border-[var(--color-brand-accent)] text-[var(--color-brand-accent)] font-bold py-3 px-8 rounded-lg hover:bg-[var(--color-brand-accent)] hover:text-[var(--color-brand-dark)] transition-colors" data-i18n="Weekly Horoscope">Недельный гороскоп</a>
      </div>
    </div>
  </section>
  <!-- About/Commitment Section -->
  <section id="about" class="py-20 bg-[var(--color-brand-dark)]">
      <div class="container mx-auto px-6">
          <div class="text-center max-w-3xl mx-auto mb-12">
              <h2 class="text-4xl md:text-5xl mb-4" data-i18n="At Celestial, we are committed to helping you discover the incredible wisdom that the stars and planets hold.">В Celestial мы стремимся помочь вам открыть невероятную мудрость, которую хранят звезды и планеты.</h2>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                  <h3 class="text-2xl mb-2" data-i18n="Accurate Readings">Точные чтения</h3>
                  <p class="text-[var(--color-brand-text-muted)]" data-i18n="Providing accurate and insightful astrological readings based on your unique birth chart.">Предоставление точных и проницательных астрологических чтений на основе вашей уникальной карты рождения.</p>
              </div>
              <div>
                  <h3 class="text-2xl mb-2" data-i18n="Reliable Guidance">Надежное руководство</h3>
                  <p class="text-[var(--color-brand-text-muted)]" data-i18n="Delivering timely and reliable guidance for your daily life through horoscope forecasts.">Предоставление своевременного и надежного руководства для вашей повседневной жизни через прогнозы гороскопа.</p>
              </div>
              <div>
                  <h3 class="text-2xl mb-2" data-i18n="Continuous Learning">Непрерывное обучение</h3>
                  <p class="text-[var(--color-brand-text-muted)]" data-i18n="Continuously learning and growing in the field of astrology to bring you the best insights.">Постоянно учимся и растем в области астрологии, чтобы принести вам лучшие идеи.</p>
              </div>
          </div>
          <div class="mt-16">
              <img src="https://placehold.co/1200x400/0d0b1c/3c2c8b?text=Mystical+Astrology+Image" alt="Astrology tools" class="rounded-lg mx-auto w-full object-cover">
          </div>
      </div>
  </section>
  <!-- Why Choose Us / Services Section -->
  <section class="py-20 bg-hero-gradient">
    <div class="container mx-auto px-6 text-center">
      <p class="text-[var(--color-brand-accent)] font-bold mb-2" data-i18n="WHY CHOOSE US">ПОЧЕМУ МЫ</p>
      <h2 class="text-4xl md:text-5xl mb-12" data-i18n="Discover Your Path in the Stars with Us">Откройте свой путь в звездах с нами</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {services.map(service => (
          <ServiceCard title={service.title} description={service.description} />
        ))}
      </div>
    </div>
  </section>
  <!-- Weekly Horoscope Section -->
  <section class="py-20 bg-[var(--color-brand-dark)]">
    <div class="container mx-auto px-6 text-center">
      <p class="text-[var(--color-brand-accent)] font-bold mb-2" data-i18n="WEEKLY HOROSCOPE">НЕДЕЛЬНЫЙ ГОРОСКОП</p>
      <h2 class="text-4xl md:text-5xl mb-12" data-i18n="What the stars have in store for you">Что звезды приготовили для вас</h2>
       <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {horoscopes.slice(0, 6).map(h => (
          <HoroscopeCard sign={h.sign} dateRange={h.dateRange} forecast={h.forecast} icon={h.icon} />
        ))}
       </div>
       <div class="mt-12">
           <a href="/horoscope" class="bg-[var(--color-brand-accent)] text-[var(--color-brand-dark)] font-bold py-3 px-8 rounded-lg hover:bg-opacity-90 transition-colors" data-i18n="More Horoscope">Больше гороскопов</a>
       </div>
    </div>
  </section>
  <!-- Membership CTA Section -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <div class="bg-hero-gradient rounded-xl p-8 md:p-16 flex flex-col md:flex-row items-center justify-between">
                <div class="text-center md:text-left mb-8 md:mb-0">
                    <h2 class="text-3xl md:text-4xl mb-2">Присоединяйтесь к нашему членству для ежедневной астрологии</h2>
                    <p class="text-[var(--color-brand-text-muted)]">Мы предоставляем все необходимое для ваших астрологических потребностей</p>
                </div>
                <a href="#pricing" class="bg-[var(--color-brand-accent)] text-[var(--color-brand-dark)] font-bold py-3 px-8 rounded-lg hover:bg-opacity-90 transition-colors flex-shrink-0">
                    Join Membership
                </a>
            </div>
        </div>
    </section>
  <!-- Testimonial Section -->
    <section class="py-20 bg-[var(--color-brand-dark)]">
        <div class="container mx-auto px-6 text-center max-w-3xl">
            <p class="text-[var(--color-brand-accent)] font-bold mb-2">TESTIMONIAL</p>
            <h2 class="text-3xl mb-6">"As a Celestial Explorer member, I look forward to my personalized monthly horoscopes and the early access to astrological forecasts."</h2>
            <p class="text-[var(--color-brand-text-muted)]">- Sarah M. - Celestial Member</p>
        </div>
    </section>
    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-hero-gradient">
        <div class="container mx-auto px-6 text-center">
            <p class="text-[var(--color-brand-accent)] font-bold mb-2">ТАРИФНЫЙ ПЛАН</p>
            <h2 class="text-4xl md:text-5xl mb-12">Посмотрите наш план членства</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <MembershipPlan
                    title="Celestial Seeker"
                    subtitle="Basic Membership"
                    price="9.99"
                    features={["Basic natal chart analysis", "Monthly newsletter with celestial insights", "Member-only discounts on special readings"]}
                />
                <MembershipPlan
                    title="Celestial Explorer"
                    subtitle="Premium Membership"
                    price="19.99"
                    features={["All features of the Seeker plan", "Access to daily horoscopes", "Exclusive early access to monthly forecasts"]}
                    popular={true}
                />
                <MembershipPlan
                    title="Celestial Visionary"
                    subtitle="VIP Membership"
                    price="49.99"
                    features={["All features of the Explorer plan", "Exclusive access to webinars and workshops", "Personalized monthly guidance sessions"]}
                />
            </div>
        </div>
    </section>
  <!-- Blog Section -->
  <section class="py-20 bg-[var(--color-brand-dark)]">
    <div class="container mx-auto px-6">
      <div class="flex justify-between items-center mb-12">
        <div>
          <p class="text-[var(--color-brand-accent)] font-bold mb-2">BLOG</p>
          <h2 class="text-4xl md:text-5xl">Cosmic Stories from Celestial</h2>
        </div>
        <a href="/blog" class="hidden md:inline-block border border-[var(--color-brand-accent)] text-[var(--color-brand-accent)] font-bold py-2 px-6 rounded-lg hover:bg-[var(--color-brand-accent)] hover:text-[var(--color-brand-dark)] transition-colors">
            More Stories
        </a>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {blogPosts.map(post => (
          <BlogPreviewCard
            imageUrl={post.imageUrl}
            date={post.date}
            title={post.title}
            link={post.link}
          />
        ))}
      </div>
       <div class="mt-12 text-center md:hidden">
           <a href="/blog" class="bg-[var(--color-brand-accent)] text-[var(--color-brand-dark)] font-bold py-3 px-8 rounded-lg hover:bg-opacity-90 transition-colors">More Stories</a>
       </div>
    </div>
  </section>
</Layout>
<style>
/* You need to define the gradients here since we removed the config file */
.bg-hero-gradient {
    background-image: radial-gradient(ellipse at 50% 50%, var(--color-brand-light-purple) 0%, var(--color-brand-dark) 70%);
}
@keyframes move-twink-back {
    from {background-position:0 0;}
    to {background-position:-10000px 5000px;}
}
#stars {
  background: transparent url(https://www.transparenttextures.com/patterns/stardust.png) repeat top center;
  animation: move-twink-back 200s linear infinite;
}

/* Light theme adjustments */
.light-theme .bg-hero-gradient {
  background-image: radial-gradient(ellipse at 50% 50%, var(--color-brand-light-purple) 0%, var(--color-brand-dark) 70%) !important;
}

.light-theme #stars {
  opacity: 0.2;
}
</style>
