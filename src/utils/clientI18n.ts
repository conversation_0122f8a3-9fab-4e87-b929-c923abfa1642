// Complete client-side internationalization system
import { completeTranslations } from '../data/translations';

export type Language = 'en' | 'ru' | 'it';

export const languages: Record<Language, string> = {
  en: 'English',
  ru: 'Русский',
  it: 'Italiano'
};

export const defaultLanguage: Language = 'en';

// Get current language from localStorage or default
export function getCurrentLanguage(): Language {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('celestial-language') as Language;
    return stored && stored in languages ? stored : defaultLanguage;
  }
  return defaultLanguage;
}

// Set current language and save to localStorage
export function setCurrentLanguage(lang: Language): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('celestial-language', lang);
    // Update HTML lang attribute
    document.documentElement.lang = lang;
    // Trigger a custom event to notify components of language change
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: lang }));
  }
}

// Apply translations to the entire page
export function applyTranslations(lang: Language): void {
  if (!completeTranslations[lang]) return;
  
  const translations = completeTranslations[lang];
  
  // Find all elements with data-i18n attribute
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    if (key && translations[key]) {
      if (element.tagName === 'INPUT' && element.hasAttribute('placeholder')) {
        (element as HTMLInputElement).placeholder = translations[key];
      } else {
        element.textContent = translations[key];
      }
    }
  });
  
  // Find all elements with data-i18n-html attribute (for HTML content)
  document.querySelectorAll('[data-i18n-html]').forEach(element => {
    const key = element.getAttribute('data-i18n-html');
    if (key && translations[key]) {
      element.innerHTML = translations[key];
    }
  });
  
  // Update language switcher display
  const langSpan = document.getElementById('current-language');
  if (langSpan) {
    langSpan.textContent = lang.toUpperCase();
  }
}

// Initialize i18n system
export function initializeI18n(): void {
  if (typeof window === 'undefined') return;
  
  const currentLang = getCurrentLanguage();
  
  // Set HTML lang attribute
  document.documentElement.lang = currentLang;
  
  // Apply translations if not English
  if (currentLang !== 'en') {
    applyTranslations(currentLang);
  }
  
  // Update language switcher display
  const langSpan = document.getElementById('current-language');
  if (langSpan) {
    langSpan.textContent = currentLang.toUpperCase();
  }
}

// Blog posts with full translations
export const blogPosts = {
  en: [
    {
      title: "New July 2024 Horoscopes Now Available!",
      excerpt: "Discover what the stars have in store for you this month. July promises to be a time of contrasts and new opportunities.",
      category: "Horoscopes",
      content: "Our comprehensive July horoscope reveals the cosmic influences that will shape your month. From Mercury retrograde to Venus in Gemini, learn how to navigate the celestial energies."
    },
    {
      title: "New Video: Understanding Your Moon Sign",
      excerpt: "Watch our latest YouTube video where we explain the importance of your moon sign in astrology and how it affects your emotions.",
      category: "Video",
      content: "Your moon sign represents your emotional nature and subconscious patterns. In this detailed video, we explore how to calculate and interpret your moon sign."
    },
    {
      title: "The Astrology of Success: How Planets Influence Your Career",
      excerpt: "Learn how to use astrological knowledge to make important career decisions and achieve professional success.",
      category: "Career",
      content: "Discover which planetary transits favor career advancement and how to time your professional moves according to cosmic cycles."
    }
  ],
  ru: [
    {
      title: "Новые гороскопы на июль 2024 уже доступны!",
      excerpt: "Откройте для себя, что звезды приготовили для вас в этом месяце. Июль обещает быть временем контрастов и новых возможностей.",
      category: "Гороскопы",
      content: "Наш подробный гороскоп на июль раскрывает космические влияния, которые будут формировать ваш месяц. От ретроградного Меркурия до Венеры в Близнецах - узнайте, как навигировать небесные энергии."
    },
    {
      title: "Новое видео: Понимание вашего лунного знака",
      excerpt: "Посмотрите наше последнее видео на YouTube, где мы объясняем важность лунного знака в астрологии и как он влияет на ваши эмоции.",
      category: "Видео",
      content: "Ваш лунный знак представляет вашу эмоциональную природу и подсознательные паттерны. В этом подробном видео мы исследуем, как рассчитать и интерпретировать ваш лунный знак."
    },
    {
      title: "Астрология успеха: Как планеты влияют на карьеру",
      excerpt: "Узнайте, как использовать астрологические знания для принятия важных карьерных решений и достижения профессионального успеха.",
      category: "Карьера",
      content: "Откройте для себя, какие планетарные транзиты благоприятствуют карьерному росту и как планировать профессиональные шаги согласно космическим циклам."
    }
  ],
  it: [
    {
      title: "I nuovi oroscopi per luglio 2024 sono ora disponibili!",
      excerpt: "Scopri cosa hanno in serbo le stelle per te questo mese. Luglio promette di essere un tempo di contrasti e nuove opportunità.",
      category: "Oroscopi",
      content: "Il nostro oroscopo completo di luglio rivela le influenze cosmiche che daranno forma al tuo mese. Da Mercurio retrogrado a Venere in Gemelli, impara a navigare le energie celesti."
    },
    {
      title: "Nuovo video: Comprendere il tuo segno lunare",
      excerpt: "Guarda il nostro ultimo video su YouTube dove spieghiamo l'importanza del segno lunare in astrologia e come influenza le tue emozioni.",
      category: "Video",
      content: "Il tuo segno lunare rappresenta la tua natura emotiva e i modelli subconsci. In questo video dettagliato, esploriamo come calcolare e interpretare il tuo segno lunare."
    },
    {
      title: "L'astrologia del successo: Come i pianeti influenzano la carriera",
      excerpt: "Scopri come utilizzare la conoscenza astrologica per prendere decisioni importanti sulla carriera e raggiungere il successo professionale.",
      category: "Carriera",
      content: "Scopri quali transiti planetari favoriscono l'avanzamento di carriera e come programmare le tue mosse professionali secondo i cicli cosmici."
    }
  ]
};

// Service content with full translations
export const serviceContent = {
  en: {
    heroSubtitle: "Discover the secrets of the universe with our professional astrological services",
    mainServicesTitle: "Our Most Popular Consultations",
    additionalServicesTitle: "Specialized Consultations",
    howItWorksTitle: "Simple Consultation Process",
    ctaTitle: "Ready to Start Your Astrological Journey?",
    ctaSubtitle: "Contact us today to book a consultation and unlock the secrets of your destiny."
  },
  ru: {
    heroSubtitle: "Откройте тайны вселенной с нашими профессиональными астрологическими услугами",
    mainServicesTitle: "Наши популярные консультации",
    additionalServicesTitle: "Специализированные консультации",
    howItWorksTitle: "Простой процесс консультации",
    ctaTitle: "Готовы начать свое астрологическое путешествие?",
    ctaSubtitle: "Свяжитесь с нами сегодня, чтобы записаться на консультацию и открыть тайны своей судьбы."
  },
  it: {
    heroSubtitle: "Scopri i segreti dell'universo con i nostri servizi astrologici professionali",
    mainServicesTitle: "Le nostre consultazioni più popolari",
    additionalServicesTitle: "Consultazioni specializzate",
    howItWorksTitle: "Processo di consultazione semplice",
    ctaTitle: "Pronto per iniziare il tuo viaggio astrologico?",
    ctaSubtitle: "Contattaci oggi per prenotare una consultazione e scoprire i segreti del tuo destino."
  }
};

// Make functions globally available
if (typeof window !== 'undefined') {
  (window as any).getCurrentLanguage = getCurrentLanguage;
  (window as any).setCurrentLanguage = setCurrentLanguage;
  (window as any).applyTranslations = applyTranslations;
  (window as any).initializeI18n = initializeI18n;
}
