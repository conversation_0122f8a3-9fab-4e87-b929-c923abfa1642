<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Language and Theme Switching</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #0d0b1c;
            color: white;
            transition: all 0.3s ease;
        }
        
        .light-theme body {
            background: #f8f9fa;
            color: #212529;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid #3c2c8b;
            border-radius: 8px;
        }
        
        button {
            margin: 5px;
            padding: 10px 15px;
            border: 1px solid #3c2c8b;
            background: transparent;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .light-theme button {
            color: #212529;
            border-color: #6c757d;
        }
        
        button:hover {
            background: #3c2c8b;
        }
        
        .light-theme button:hover {
            background: #6c757d;
            color: white;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: rgba(60, 44, 139, 0.2);
            border-radius: 8px;
        }
        
        .light-theme .status {
            background: rgba(108, 117, 125, 0.2);
        }
    </style>
</head>
<body>
    <h1>Language and Theme Switching Test</h1>
    
    <div class="controls">
        <h3>Language Controls</h3>
        <button onclick="setLanguage('en')">English</button>
        <button onclick="setLanguage('ru')">Русский</button>
        <button onclick="setLanguage('it')">Italiano</button>
        
        <h3>Theme Controls</h3>
        <button onclick="setTheme('dark')">Dark Theme</button>
        <button onclick="setTheme('light')">Light Theme</button>
    </div>
    
    <div class="status">
        <h3>Current Status</h3>
        <p>Language: <span id="current-lang">Loading...</span></p>
        <p>Theme: <span id="current-theme">Loading...</span></p>
        <p>LocalStorage Language: <span id="storage-lang">Loading...</span></p>
        <p>LocalStorage Theme: <span id="storage-theme">Loading...</span></p>
    </div>
    
    <div class="status">
        <h3>Sample Translations</h3>
        <p id="sample-text">Loading translations...</p>
    </div>

    <script type="module">
        // Import our utility functions (simulated)
        const languages = {
            en: 'English',
            ru: 'Русский',
            it: 'Italiano'
        };

        const translations = {
            en: {
                welcome: "Welcome to Celestial",
                description: "Unveiling the Universe Within You"
            },
            ru: {
                welcome: "Добро пожаловать в Celestial",
                description: "Раскрываем Вселенную Внутри Вас"
            },
            it: {
                welcome: "Benvenuti in Celestial",
                description: "Svelando l'Universo Dentro di Te"
            }
        };

        function getCurrentLanguage() {
            const stored = localStorage.getItem('celestial-language');
            return stored && stored in languages ? stored : 'en';
        }

        function setCurrentLanguage(lang) {
            localStorage.setItem('celestial-language', lang);
            updateDisplay();
        }

        function getCurrentTheme() {
            const stored = localStorage.getItem('celestial-theme');
            return stored === 'light' ? 'light' : 'dark';
        }

        function setCurrentTheme(theme) {
            localStorage.setItem('celestial-theme', theme);
            document.documentElement.classList.toggle('light-theme', theme === 'light');
            updateDisplay();
        }

        function updateDisplay() {
            const lang = getCurrentLanguage();
            const theme = getCurrentTheme();
            
            document.getElementById('current-lang').textContent = languages[lang];
            document.getElementById('current-theme').textContent = theme;
            document.getElementById('storage-lang').textContent = localStorage.getItem('celestial-language') || 'null';
            document.getElementById('storage-theme').textContent = localStorage.getItem('celestial-theme') || 'null';
            
            const t = translations[lang];
            document.getElementById('sample-text').innerHTML = `
                <strong>${t.welcome}</strong><br>
                ${t.description}
            `;
        }

        // Global functions for buttons
        window.setLanguage = function(lang) {
            setCurrentLanguage(lang);
        };

        window.setTheme = function(theme) {
            setCurrentTheme(theme);
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            const theme = getCurrentTheme();
            if (theme === 'light') {
                document.documentElement.classList.add('light-theme');
            }
            updateDisplay();
        });
    </script>
</body>
</html>
